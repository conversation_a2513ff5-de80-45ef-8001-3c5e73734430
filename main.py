#!/usr/bin/env python3
"""
Playwright Automation Script Generator
Main entry point for the CLI application
"""

import click
from loguru import logger
from src.config import load_config
from src.cli import process_url
from src.utils import setup_logging


@click.command()
@click.option(
    "--url", "-u", 
    required=True, 
    help="URL of the website to analyze"
)
@click.option(
    "--action", "-a", 
    required=True, 
    help="Description of the action to automate (e.g., 'login to the site')"
)
@click.option(
    "--browser", "-b", 
    default="chromium", 
    type=click.Choice(["chromium", "firefox", "webkit"]),
    help="Browser to use for capturing the website"
)
@click.option(
    "--output", "-o", 
    default="./output_script.py", 
    help="Path to save the generated script"
)
@click.option(
    "--config", "-c",
    default="./config/config.yaml",
    help="Path to configuration file"
)
@click.option(
    "--verbose", "-v", 
    is_flag=True, 
    help="Enable verbose output"
)
def main(url, action, browser, output, config, verbose):
    """
    Generate a Playwright automation script based on a URL and action description.
    Uses OpenAI's vision models to analyze the webpage and create accurate automation scripts.
    """
    # Initialize logging
    setup_logging(verbose)
    
    logger.info(f"Starting script generation for URL: {url}")
    logger.info(f"Action to automate: {action}")
    
    # Load configuration
    config_data = load_config(config)
    
    # Process URL and generate script
    result = process_url(url, action, browser, output, config_data)
    
    if result:
        logger.success(f"Script successfully generated and saved to: {output}")
    else:
        logger.error("Failed to generate script")


if __name__ == "__main__":
    main()
