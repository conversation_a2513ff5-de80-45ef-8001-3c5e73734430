"""
Enhanced script generation with robust anti-bot protection and error handling
"""

import os
import httpx
from typing import Dict, Optional, <PERSON>ple
from loguru import logger
from openai import OpenAI, AzureOpenAI

from .dom_processor import clean_and_analyze_dom, validate_selectors_with_playwright


def generate_robust_script(dom_content: str, vision_analysis: str, action_text: str, 
                          url: str, page_title: str, config: Dict) -> <PERSON><PERSON>[Optional[str], Optional[Dict]]:
    """
    Generate a robust Playwright script with anti-bot protection and comprehensive error handling
    
    Args:
        dom_content: HTML DOM content
        vision_analysis: Analysis from Vision API
        action_text: Description of the action to automate
        url: URL of the website
        page_title: Title of the webpage
        config: Configuration dictionary
    
    Returns:
        Tuple of (script_content, dom_analysis)
    """
    openai_config = config.get('openai', {})
    
    # Initialize OpenAI client
    client = _initialize_openai_client(openai_config)
    if not client:
        return None, None
    
    # Process DOM content for better analysis
    logger.info("Processing DOM content for enhanced analysis")
    dom_analysis = clean_and_analyze_dom(dom_content, action_text)
    
    # Validate selectors against the actual page
    logger.info("Validating selectors against the live page")
    if dom_analysis['selector_suggestions']:
        try:
            validated_selectors = validate_selectors_with_playwright(url, dom_analysis['selector_suggestions'])
            dom_analysis['validated_selectors'] = validated_selectors
            logger.info(f"Validated {len(validated_selectors)} selectors successfully")
        except Exception as e:
            logger.error(f"Error during selector validation: {str(e)}")
            dom_analysis['validated_selectors'] = []
    else:
        dom_analysis['validated_selectors'] = []
    
    try:
        logger.info("Generating robust Playwright script")
        
        # Create enhanced system prompt
        system_prompt = _build_robust_system_prompt()
        
        # Create user prompt with all context
        user_prompt = _build_user_prompt(action_text, url, page_title, vision_analysis, dom_analysis)
        
        # Get model configuration
        model = openai_config.get('azure_deployment_name_completion', 'gpt-4o') if openai_config.get('use_azure') else openai_config.get('model', 'gpt-4o')
        max_tokens = openai_config.get('max_tokens', 8000)
        temperature = 0.1
        
        # Generate the script
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        script_content = response.choices[0].message.content.strip()
        
        # Clean up the response
        script_content = _clean_script_response(script_content)
        
        logger.info("Successfully generated robust script")
        return script_content, dom_analysis
        
    except Exception as e:
        logger.error(f"Error generating robust script: {str(e)}")
        return None, None


def _initialize_openai_client(openai_config: Dict):
    """Initialize OpenAI client based on configuration"""
    api_key = openai_config.get('api_key')
    use_azure = openai_config.get('use_azure', True)
    
    if not api_key:
        api_key = os.environ.get('OPENAI_API_KEY', '')
        if not api_key:
            logger.error("OpenAI API key not provided")
            return None
    
    if use_azure:
        azure_endpoint = openai_config.get('azure_endpoint', '')
        if not azure_endpoint:
            azure_endpoint = os.environ.get('AZURE_OPENAI_ENDPOINT', '')
        
        azure_api_version = openai_config.get('azure_api_version', '2023-05-15')
        
        clean_http_client = httpx.Client()
        return AzureOpenAI(
            api_key=api_key,
            api_version=azure_api_version,
            azure_endpoint=azure_endpoint,
            http_client=clean_http_client
        )
    else:
        return OpenAI(api_key=api_key)


def _build_robust_system_prompt() -> str:
    """Build the system prompt for robust script generation"""
    return """You are an expert Playwright automation engineer specializing in creating robust, production-ready automation scripts that handle real-world challenges including anti-bot protection, dynamic content, and varying network conditions.

## CORE PRINCIPLES:
1. **Anti-Bot Protection**: Configure browsers to avoid detection with realistic user agents, viewports, and human-like behavior
2. **Reliability First**: Always implement multiple selector strategies with comprehensive fallbacks
3. **Robust Error Handling**: Handle timeouts, missing elements, CAPTCHAs, and edge cases gracefully
4. **Smart Waiting**: Use appropriate wait conditions with extended timeouts for dynamic content
5. **Maintainability**: Write clean, well-commented, modular code with detailed logging

## MANDATORY SCRIPT TEMPLATE:
Your script MUST include these robust features:

```python
import sys
import time
import random

try:
    from playwright.sync_api import sync_playwright, TimeoutError
except ImportError:
    print("Error: Playwright module not found. Please install it using 'pip install playwright'.")
    sys.exit(1)

def create_robust_browser_context(playwright):
    browser = playwright.chromium.launch(
        headless=False,  # Set to True for production
        args=[
            '--no-sandbox',
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ]
    )
    
    context = browser.new_context(
        user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        viewport={'width': 1920, 'height': 1080}
    )
    
    return browser, context

def human_like_delay(min_ms=500, max_ms=2000):
    delay = random.uniform(min_ms/1000, max_ms/1000)
    time.sleep(delay)

def wait_for_element_with_fallbacks(page, selectors, timeout=30000, action_name="find element"):
    print(f"🔍 Attempting to {action_name}...")
    
    for i, selector in enumerate(selectors):
        try:
            print(f"  Trying selector {i+1}/{len(selectors)}: {selector}")
            page.wait_for_selector(selector, timeout=timeout//len(selectors))
            print(f"  ✅ Found element with selector: {selector}")
            return page.locator(selector)
        except TimeoutError:
            print(f"  ❌ Selector {selector} timed out")
            continue
        except Exception as e:
            print(f"  ❌ Selector {selector} failed: {e}")
            continue
    
    raise Exception(f"Failed to {action_name} with any of the provided selectors")

def check_for_captcha_or_blocking(page):
    try:
        page_content = page.content().lower()
        blocking_indicators = ['captcha', 'recaptcha', 'unusual traffic', 'blocked']
        
        for indicator in blocking_indicators:
            if indicator in page_content:
                print(f"⚠️ Detected potential blocking: {indicator}")
                return True
        return False
    except:
        return False
```

## MANDATORY REQUIREMENTS:
1. **ALWAYS use the robust browser configuration** with anti-detection settings
2. **ALWAYS implement multiple selector strategies** with fallbacks for every element interaction
3. **ALWAYS add human-like delays** between actions
4. **ALWAYS use extended timeouts** (minimum 30 seconds for page loads, 10 seconds for elements)
5. **ALWAYS check for CAPTCHA/blocking** after navigation
6. **ALWAYS implement comprehensive error handling** with specific error messages
7. **ALWAYS use wait_for_element_with_fallbacks()** instead of basic wait_for_selector()
8. **ALWAYS add detailed logging** with progress indicators (🚀, 📍, ✅, ❌, ⚠️)

Your response must be ONLY valid, executable Python code with NO markdown formatting."""


def _build_user_prompt(action_text: str, url: str, page_title: str, 
                      vision_analysis: str, dom_analysis: Dict) -> str:
    """Build the user prompt with all context"""
    
    # Format validated selectors
    validated_selectors_text = ""
    if dom_analysis.get('validated_selectors'):
        validated_selectors_text = "## VALIDATED SELECTORS (USE THESE FIRST):\n"
        for selector in dom_analysis['validated_selectors'][:10]:
            validated_selectors_text += f"✅ {selector['selector']} - {selector['element_count']} elements found\n"
    else:
        validated_selectors_text = "## VALIDATED SELECTORS: None available\n"
    
    # Format DOM suggestions
    dom_suggestions_text = ""
    if dom_analysis.get('selector_suggestions'):
        dom_suggestions_text = "## DOM SELECTOR SUGGESTIONS:\n"
        for suggestion in dom_analysis['selector_suggestions'][:10]:
            dom_suggestions_text += f"- {suggestion['selector']} ({suggestion['element_count']} elements)\n"
    
    return f"""Generate a robust Playwright automation script for this specific action: "{action_text}"

**TARGET WEBSITE:** {url}
**PAGE TITLE:** {page_title}

## VISUAL ANALYSIS:
{vision_analysis}

{validated_selectors_text}

{dom_suggestions_text}

## SPECIFIC REQUIREMENTS:

1. **Primary Objective**: {action_text}

2. **Robust Implementation**: 
   - Use the robust browser configuration template provided
   - Implement multiple selector strategies with fallbacks
   - Add human-like delays between actions
   - Include comprehensive error handling
   - Add detailed progress logging

3. **Selector Strategy**: 
   - Use VALIDATED SELECTORS first (marked with ✅) - these are tested and working
   - Implement fallback selectors for robustness
   - Handle cases where elements might not be immediately visible

4. **Error Handling**: Account for:
   - Elements that load dynamically
   - Timeouts and network delays
   - CAPTCHA or blocking detection
   - Page structure variations

5. **Success Verification**: Include appropriate checks to verify the action was successful

Generate a complete, production-ready script that implements all these requirements."""


def _clean_script_response(script_content: str) -> str:
    """Clean the LLM response to extract just the Python code"""
    script_content = script_content.strip()
    
    if script_content.startswith('```python'):
        script_content = script_content[9:]
    elif script_content.startswith('```'):
        script_content = script_content[3:]
    
    if script_content.endswith('```'):
        script_content = script_content[:-3]
    
    return script_content.strip()
