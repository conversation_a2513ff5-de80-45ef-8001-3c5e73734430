"""
Configuration management for the Playwright Script Generator
"""

import os
import yaml
from loguru import logger


def load_config(config_path="./config/config.yaml"):
    """
    Load configuration from YAML file
    
    Args:
        config_path (str): Path to the configuration file
        
    Returns:
        dict: Configuration data
    """
    try:
        with open(config_path, 'r') as config_file:
            config = yaml.safe_load(config_file)
        
        # Override with environment variables if available
        if not config['openai']['api_key']:
            config['openai']['api_key'] = os.environ.get('OPENAI_API_KEY')
            if not config['openai']['api_key']:
                logger.warning("OpenAI API key not found in config or environment variables")
        
        return config
    except FileNotFoundError:
        logger.error(f"Configuration file not found: {config_path}")
        return create_default_config(config_path)
    except yaml.YAMLError:
        logger.error(f"Error parsing configuration file: {config_path}")
        return create_default_config(config_path)


def create_default_config(config_path):
    """
    Create a default configuration file if none exists
    
    Args:
        config_path (str): Path to save the default configuration
        
    Returns:
        dict: Default configuration data
    """
    default_config = {
        'openai': {
            'api_key': os.environ.get('OPENAI_API_KEY', ''),
            'model': 'gpt-4o',
            'max_tokens': 4000,
            'temperature': 0.2,
            'use_azure': True,
            'azure_endpoint': os.environ.get('AZURE_OPENAI_ENDPOINT', ''),
            'azure_api_version': '2023-05-15',
            'azure_deployment_name_vision': 'gpt-4o',
            'azure_deployment_name_completion': 'gpt-4o'
        },
        'playwright': {
            'timeout': 30000,
            'viewport': {'width': 1280, 'height': 720},
            'headless': True
        },
        'script': {
            'template_path': '',
            'add_comments': True,
            'validate_selectors': True,
            'include_assertions': True
        },
        'output': {
            'indent_size': 4,
            'force_overwrite': False
        },
        'validation': {
            'enabled': True,
            'max_retries': 3,
            'timeout_per_execution': 60,
            'success_criteria': {
                'script_executes_without_errors': True,
                'expected_output_format': True,
                'minimum_results_count': 1,
                'no_timeout_errors': True,
                'no_assertion_errors': True
            },
            'save_intermediate_versions': True,
            'detailed_logging': True
        }
    }
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    try:
        with open(config_path, 'w') as config_file:
            yaml.dump(default_config, config_file, default_flow_style=False)
        logger.info(f"Created default configuration at: {config_path}")
    except Exception as e:
        logger.error(f"Failed to create default configuration: {str(e)}")
    
    return default_config
