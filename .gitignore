# =============================================================================
# Playwright Script Generator - Intelligent .gitignore
# =============================================================================

# =============================================================================
# SECURITY & SECRETS - CRITICAL: Never commit sensitive information
# =============================================================================
# Environment files containing API keys and secrets
.env
.env.local
.env.*.local
.env.production
.env.staging
.env.development

# Configuration files with secrets (customize as needed)
config/config.yaml
config/production.yaml
config/staging.yaml
**/secrets.yaml
**/secrets.json

# API keys and credential files
*.key
*.pem
*.p12
*.pfx
credentials.json
service-account.json

# =============================================================================
# PYTHON - Standard Python ignores
# =============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# VIRTUAL ENVIRONMENTS
# =============================================================================
# Virtual environment directories
venv/
env/
ENV/
.venv/
.env/
.ENV/
venv.bak/
env.bak/

# Conda environments
.conda/

# =============================================================================
# PLAYWRIGHT & BROWSER AUTOMATION
# =============================================================================
# Playwright browsers and dependencies
playwright-browsers/
ms-playwright/

# Browser downloads and cache
.playwright/
playwright-report/
test-results/

# Screenshots and videos from tests
screenshots/
videos/
traces/

# Browser profiles and user data
browser-profiles/
user-data-dir/

# =============================================================================
# LOGS & TEMPORARY FILES
# =============================================================================
# Log files
logs/
*.log
*.log.*
log/

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# =============================================================================
# PROJECT-SPECIFIC OUTPUT FILES
# =============================================================================
# Generated Playwright scripts
output_script.py
generated_scripts/
output/
outputs/

# Captured screenshots and DOM files
captures/
dom_snapshots/
page_screenshots/

# Analysis results
analysis_results/
vision_analysis/

# =============================================================================
# IDE & EDITOR FILES
# =============================================================================
# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.vim/

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Atom
.atom/

# JetBrains IDEs
.idea/
*.iml
out/

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# DEPENDENCIES & PACKAGE MANAGERS
# =============================================================================
# Node.js (if using any Node tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# =============================================================================
# MISCELLANEOUS
# =============================================================================
# Backup files
*.bak
*.backup
*.old
*.orig

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Database files
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
cache/

# =============================================================================
# PROJECT NOTES
# =============================================================================
# Personal notes and TODO files
TODO.md
NOTES.md
notes/
docs/personal/
